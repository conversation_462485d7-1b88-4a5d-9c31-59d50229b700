<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>高程测量</title>
    <link rel="stylesheet" href="/static/style.css" />
  </head>
  <body>
    <main class="container">
      <header>
        <h1>高程测量</h1>
      </header>

      <form id="run-form" class="card" onsubmit="return false;">
        <div class="row">
          <label for="input_path" class="label-io">输入文件夹路径</label>
          <div class="input-with-btn">
            <input id="input_path" name="input_path" type="text" placeholder="/home/<USER>/Data/ytj/DEM" value="/home/<USER>/Data/ytj/DEM" />
            <button type="button" class="browse secondary" data-target="input_path" data-only-dirs="true">浏览…</button>
          </div>
        </div>
        <div class="row">
          <label for="output_dir" class="label-io">输出目录</label>
          <div class="input-with-btn">
            <input id="output_dir" name="output_dir" type="text" placeholder="/home/<USER>/Data/ytj/DEM" value="/home/<USER>/Data/ytj/DEM/result" />
            <button type="button" class="browse secondary" data-target="output_dir" data-only-dirs="true">浏览…</button>
          </div>
        </div>

        <div class="row">
          <label for="control_path" class="label-io">控制点文件 (.xls)</label>
          <div class="input-with-btn">
            <input id="control_path" name="control_path" type="text" placeholder="/home/<USER>/Data/ytj/DEM/03控制点成果表G1G3.xls" value="/home/<USER>/Data/ytj/DEM/03控制点成果表G1G3.xls" />
            <button type="button" class="browse secondary" data-target="control_path" data-only-dirs="false" data-ext=".xls,.xlsx">浏览…</button>
            <button id="show-cp-btn" type="button" class="ghost" disabled>控制点结果</button>
          </div>
        </div>

        <div class="row">
          <label class="label-param">计算设备</label>
          <div style="display:flex; gap:16px; align-items:center;">
            <label class="checkbox"><input id="device_cpu" name="device" type="radio" value="cpu" checked /> <span>CPU</span></label>
            <label class="checkbox"><input id="device_gpu" name="device" type="radio" value="gpu" /> <span>GPU (CUDA)</span></label>
          </div>
        </div>

        <div class="grid">
          <div class="col">
            <label class="checkbox label-param">
              <input id="gb_enable" type="checkbox" checked />
              <span>使用 GB 比值过滤</span>
            </label>
            <div class="row">
              <label for="gb_threshold" class="label-param">GB 比值阈值</label>
              <div class="slider">
                <input id="gb_threshold" type="range" min="0.5" max="3.0" step="0.05" value="1.2" />
                <output id="gb_threshold_val">1.20</output>
              </div>
            </div>
            <div class="row">
              <label for="sample_mut_m" class="label-param">突变点采样间隔 (m)</label>
              <div class="slider">
                <input id="sample_mut_m" type="range" min="1" max="100" step="1" value="10" />
                <output id="sample_mut_m_val">10</output>
              </div>
            </div>
          </div>

          <div class="col">
            <div class="row">
              <label for="avg_threshold" class="label-param">平均分阈值 (0–1)</label>
              <div class="slider">
                <input id="avg_threshold" type="range" min="0.10" max="0.90" step="0.01" value="0.20" />
                <output id="avg_threshold_val">0.50</output>
              </div>
            </div>

            <div class="row">
              <label for="rgb_limit" class="label-param">单通道下限 (0–255)</label>
              <div class="slider">
                <input id="rgb_limit" type="range" min="0" max="255" step="1" value="20" />
                <output id="rgb_limit_val">20</output>
              </div>
            </div>
            <div class="row">
              <label for="sample_ground_m" class="label-param">地面采样间隔 (m)</label>
              <div class="slider">
                <input id="sample_ground_m" type="range" min="1" max="100" step="1" value="20" />
                <output id="sample_ground_m_val">20</output>
              </div>
            </div>
          </div>
        </div>

        <div class="actions">
          <button id="run-btn" class="primary" type="button">开始突变点提取</button>
          <button id="stop-btn" class="danger" type="button" disabled>停止</button>
        </div>
      </form>

      <section class="card">
        <div class="row">
          <label class="label-param">俯视图预览</label>
        </div>
        <div class="row" style="display:flex; gap:12px; align-items:center;">
          <div style="flex:1;">
            <label for="las-select-raw" class="label-param" style="margin-bottom:6px;">选择 LAS（原始俯视图）</label>
            <div class="input-with-btn">
              <select id="las-select-raw" style="width:100%; padding:10px 12px; border:1px solid var(--border); border-radius: 8px;"></select>
              <button id="preview-las-btn" type="button" class="ghost">场景浏览</button>
            </div>
          </div>
          <div style="flex:1;">
            <label for="las-select-sel" class="label-param" style="margin-bottom:6px;">选择 LAS（突变点预览）</label>
            <div class="input-with-btn">
              <select id="las-select-sel" style="width:100%; padding:10px 12px; border:1px solid var(--border); border-radius: 8px;"></select>
              <button id="preview-sel-btn" type="button" class="ghost">预览突变点</button>
            </div>
          </div>
        </div>
        <div class="preview-grid">
          <div class="pane">
            <div class="pane-title">原始点云俯视图</div>
            <div class="pane-canvas">
              <img id="img-las" alt="LAS 俯视图预览" />
            </div>
          </div>
          <div class="pane">
            <div class="pane-title">突变点俯视图</div>
            <div class="pane-canvas">
              <img id="img-sel" alt="突变点俯视图预览" />
            </div>
          </div>
        </div>
      </section>

      <section class="card">
        <div class="row">
          <label class="label-log label-param">运行日志</label>
        </div>
        <pre id="log" class="log" spellcheck="false"></pre>
        <div class="log-resizer" title="拖动调整日志高度"></div>
      </section>

      <!-- 文件浏览弹窗 -->
      <div id="fs-modal" class="modal hidden" role="dialog" aria-modal="true">
        <div class="modal-content card">
          <div class="modal-header">
            <div id="fs-current-path" class="modal-header-path"></div>
            <div class="modal-actions">
              <button id="fs-up" type="button" class="secondary">上一级</button>
              <button id="fs-confirm" type="button" class="primary" disabled>选择</button>
              <button id="fs-cancel" type="button" class="ghost">取消</button>
            </div>
          </div>
          <div id="fs-list" class="fs-list" tabindex="0" aria-label="文件列表"></div>
        </div>
      </div>

      <!-- 控制点结果弹窗 -->
      <div id="cp-modal" class="modal hidden" role="dialog" aria-modal="true">
        <div class="modal-content card cp-modal">
          <div class="modal-header">
            <div class="modal-title">控制点检测结果（汇总所有 LAS）</div>
            <div class="modal-actions">
              <button id="cp-close" type="button" class="ghost">关闭</button>
            </div>
          </div>
          <div class="cp-summary">
            <div id="cp-meta" class="cp-meta text-muted"></div>
            <div class="cp-table-wrapper">
              <table id="cp-table" class="cp-table">
                <thead>
                  <tr>
                    <th>LAS</th>
                    <th>代号</th>
                    <th>状态</th>
                    <th>X</th>
                    <th>Y</th>
                    <th>高程</th>
                    <th>90分位</th>
                    <th>中位</th>
                    <th>均值</th>
                    <th>最高</th>
                    <th>最低</th>
                    <th>Δh(cm)</th>
                    <th>点数</th>
                  </tr>
                </thead>
                <tbody></tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

    </main>

    <script>
      const $ = (sel) => document.querySelector(sel);
      const inputPath = $('#input_path');
      const outputDir = $('#output_dir');
      const controlPath = $('#control_path');
      const lasSelectRaw = $('#las-select-raw');
      const lasSelectSel = $('#las-select-sel');
      const deviceCpu = $('#device_cpu');
      const deviceGpu = $('#device_gpu');
      const runBtn = $('#run-btn');
      const stopBtn = $('#stop-btn');
      const previewLasBtn = $('#preview-las-btn');
      const previewSelBtn = $('#preview-sel-btn');
      const showCpBtn = $('#show-cp-btn');
      const log = $('#log');
      const gbEnable = $('#gb_enable');
      const gbThr = $('#gb_threshold');
      const gbThrVal = $('#gb_threshold_val');
      const avgThr = $('#avg_threshold');
      const avgThrVal = $('#avg_threshold_val');
      const rgbLimit = $('#rgb_limit');
      const rgbLimitVal = $('#rgb_limit_val');
      const sampleMut = $('#sample_mut_m');
      const sampleMutVal = $('#sample_mut_m_val');
      const sampleGrd = $('#sample_ground_m');
      const sampleGrdVal = $('#sample_ground_m_val');
      const fsModal = $('#fs-modal');
      const fsList = $('#fs-list');
      const fsPathEl = $('#fs-current-path');
      const fsUpBtn = $('#fs-up');
      const fsConfirmBtn = $('#fs-confirm');
      const fsCancelBtn = $('#fs-cancel');
      const cpModal = $('#cp-modal');
      const cpCloseBtn = $('#cp-close');
      const cpMeta = $('#cp-meta');
      const cpTableBody = document.querySelector('#cp-table tbody');
      // 提前声明，避免在 resetCpSummary() 早于声明执行时触发 TDZ 错误
      var cpSummaryList = [];

      function syncOutput(input, out, formatter = (v) => v) {
        const update = () => (out.textContent = formatter(input.value));
        input.addEventListener('input', update);
        update();
      }

      syncOutput(gbThr, gbThrVal, (v) => Number(v).toFixed(2));
      syncOutput(avgThr, avgThrVal, (v) => Number(v).toFixed(2));
      syncOutput(rgbLimit, rgbLimitVal, (v) => String(Math.round(Number(v))));
      syncOutput(sampleMut, sampleMutVal, (v) => String(Math.round(Number(v))));
      syncOutput(sampleGrd, sampleGrdVal, (v) => String(Math.round(Number(v))));

      // Range 填充：拖动时左侧为深蓝
      function updateRangeFill(el) {
        const min = Number(el.min || 0);
        const max = Number(el.max || 100);
        const val = Number(el.value || 0);
        const pct = ((val - min) / (max - min)) * 100;
        el.style.setProperty('--range-pct', pct + '%');
      }
      document.querySelectorAll('input[type="range"]').forEach(el => {
        el.addEventListener('input', () => updateRangeFill(el));
        updateRangeFill(el);
      });

      resetCpSummary();

      let source = null;
      function setRunning(running) {
        runBtn.disabled = running;
        stopBtn.disabled = !running;
      }

      function appendLog(text) {
        log.textContent += (text.endsWith('\n') ? text : text + '\n');
        log.scrollTop = log.scrollHeight;
      }

      const CP_SUMMARY_PREFIX = 'CONTROL_POINT_SUMMARY ';

      function resetCpSummary() {
        cpSummaryList = [];
        showCpBtn.disabled = true;
        cpMeta.textContent = '暂无结果';
        if (cpTableBody) {
          cpTableBody.innerHTML = '<tr><td class="cp-empty" colspan="13">暂无数据</td></tr>';
        }
        closeCpModal();
      }

      function renderCpSummary() {
        if (!Array.isArray(cpSummaryList) || cpSummaryList.length === 0) {
          cpMeta.textContent = '暂无结果';
          if (cpTableBody) cpTableBody.innerHTML = '<tr><td class="cp-empty" colspan="13">暂无数据</td></tr>';
          return;
        }
        const fmt = (val, digits = 3) => (typeof val === 'number' && Number.isFinite(val) ? val.toFixed(digits) : '--');
        // 汇总元信息
        let totalValid = 0, totalRows = 0, totalInRange = 0;
        cpSummaryList.forEach(item => {
          const summ = item.summary || {};
          totalValid += Number(summ.valid_points || 0);
          totalRows += Number(summ.total_rows || 0);
          totalInRange += Number(summ.in_range || 0);
        });
        cpMeta.textContent = `LAS 数量 ${cpSummaryList.length}，有效控制点 ${totalValid}/${totalRows}，范围内 ${totalInRange}`;

        if (!cpTableBody) return;
        cpTableBody.innerHTML = '';
        let any = false;
        cpSummaryList.forEach(item => {
          const las = item.las || '';
          const summ = item.summary || {};
          const pts = Array.isArray(summ.points) ? summ.points : [];
          pts.forEach(pt => {
            any = true;
            const delta = typeof pt.delta === 'number' && Number.isFinite(pt.delta) ? pt.delta : null;
            const absDelta = delta == null ? null : Math.abs(delta);
            let deltaClass = '';
            if (absDelta != null) {
              if (absDelta > 0.10) deltaClass = 'delta-high';
              else if (absDelta >= 0.05) deltaClass = 'delta-mid';
            }
            const row = document.createElement('tr');
            row.innerHTML = `
              <td>${las}</td>
              <td>${pt.code || ''}</td>
              <td>${pt.status || ''}</td>
              <td>${fmt(pt.x)}</td>
              <td>${fmt(pt.y)}</td>
              <td>${fmt(pt.z)}</td>
              <td>${fmt(pt.p90)}</td>
              <td>${fmt(pt.p50)}</td>
              <td>${fmt(pt.mean)}</td>
              <td>${fmt(pt.zmax)}</td>
              <td>${fmt(pt.zmin)}</td>
              <td class="${deltaClass}">${delta == null ? '--' : (delta * 100).toFixed(1)}</td>
              <td>${(pt.neighbor_count == null ? 0 : pt.neighbor_count)}</td>
            `;
            cpTableBody.appendChild(row);
          });
        });
        if (!any) cpTableBody.innerHTML = '<tr><td class="cp-empty" colspan="13">范围内没有可用控制点</td></tr>';
      }

      function openCpModal() {
        if (!cpSummaryList || !cpSummaryList.length) return;
        renderCpSummary();
        cpModal.classList.remove('hidden');
      }

      function closeCpModal() {
        if (!cpModal.classList.contains('hidden')) {
          cpModal.classList.add('hidden');
        }
      }

      function buildUrl() {
        const params = new URLSearchParams({
          input_path: inputPath.value.trim(),
          output_dir: outputDir.value.trim(),
          device: (deviceGpu && deviceGpu.checked) ? 'gpu' : 'cpu',
          gb_enable: gbEnable.checked ? 'true' : 'false',
          gb_threshold: gbThr.value,
          avg_threshold: avgThr.value,
          rgb_limit: rgbLimit.value,
          control_path: controlPath.value.trim(),
          sample_mut_m: sampleMut.value,
          sample_ground_m: sampleGrd.value,
        });
        return `/run?${params.toString()}`;
      }

      const PREVIEW_SIZE = 800; // 后端固定生成正方形画布，保持两图一致
      function refreshLasPreview() {
        const p = (lasSelectRaw && lasSelectRaw.value) ? lasSelectRaw.value : '';
        if (!p) { appendLog('请先从下拉列表选择 LAS'); return; }
        const ts = Date.now();
        const url = `/preview/las?input_path=${encodeURIComponent(p)}&size=${PREVIEW_SIZE}&_=${ts}`;
        const img = document.getElementById('img-las');
        img.src = url;
      }

      function refreshSelPreview() {
        const d = outputDir.value.trim();
        const p = (lasSelectSel && lasSelectSel.value) ? lasSelectSel.value : '';
        if (!d) { appendLog('请先指定输出目录'); return; }
        if (!p) { appendLog('请先从下拉列表选择 LAS'); return; }
        const ts = Date.now();
        const url = `/preview/selection?output_dir=${encodeURIComponent(d)}&las=${encodeURIComponent(p)}&size=${PREVIEW_SIZE}&_=${ts}`;
        const img = document.getElementById('img-sel');
        img.src = url;
      }

      runBtn.addEventListener('click', () => {
        if (!inputPath.value.trim() || !outputDir.value.trim()) {
          appendLog('请填写输入文件路径与输出目录\n');
          return;
        }
        log.textContent = '';
        resetCpSummary();
        setRunning(true);
        const url = buildUrl();
        source = new EventSource(url);
        source.onmessage = (ev) => {
          const text = ev.data || '';
          if (text.startsWith(CP_SUMMARY_PREFIX)) {
            const payload = text.slice(CP_SUMMARY_PREFIX.length);
            try {
              const obj = JSON.parse(payload);
              const entry = obj && obj.summary ? obj : { las: 'unknown', summary: obj };
              const idx = cpSummaryList.findIndex(it => it.las === entry.las);
              if (idx >= 0) cpSummaryList[idx] = entry; else cpSummaryList.push(entry);
              showCpBtn.disabled = cpSummaryList.length === 0;
              renderCpSummary();
            } catch (err) {
              console.error('解析控制点结果失败', err);
            }
            return;
          }
          appendLog(text);
        };
        source.addEventListener('done', () => {
          appendLog('—— 任务完成 ——');
          source.close();
          setRunning(false);
          // 任务完成后自动刷新突变点俯视图
          refreshSelPreview();
        });
        source.addEventListener('error', (ev) => {
          if (ev.data) appendLog(String(ev.data));
          appendLog('发生错误或连接中断');
          try { source.close(); } catch {}
          setRunning(false);
        });
      });

      stopBtn.addEventListener('click', () => {
        if (source) {
          try { source.close(); } catch {}
          appendLog('已请求停止（关闭流）');
        }
        setRunning(false);
      });

      showCpBtn.addEventListener('click', () => {
        if (cpSummaryList && cpSummaryList.length) openCpModal();
      });
      cpCloseBtn.addEventListener('click', closeCpModal);
      cpModal.addEventListener('click', (e) => {
        if (e.target === cpModal) closeCpModal();
      });

      // --- 简易服务端文件浏览器 ---
      let fsState = {
        targetInput: null,
        onlyDirs: false,
        ext: null,
        cwd: null,
        selected: null,
      };

      async function fsFetch(path = '') {
        const params = new URLSearchParams();
        if (path) params.set('path', path);
        if (fsState.onlyDirs) params.set('only_dirs', 'true');
        if (fsState.ext) params.set('ext', fsState.ext);
        const res = await fetch(`/fs/list?${params.toString()}`);
        if (!res.ok) throw new Error('读取目录失败');
        return await res.json();
      }

      function openFsModal(targetInputId, onlyDirs = false, ext = null) {
        fsState = { targetInput: $("#"+targetInputId), onlyDirs, ext, cwd: null, selected: null };
        fsConfirmBtn.disabled = true;
        fsModal.classList.remove('hidden');
        loadFs();
      }

      function closeFsModal() {
        fsModal.classList.add('hidden');
        fsList.innerHTML = '';
        fsState = { targetInput: null, onlyDirs: false, ext: null, cwd: null, selected: null };
      }

      async function loadFs(nextPath) {
        try {
          const basePath = (nextPath != null && nextPath !== '') ? nextPath : ((fsState.cwd != null && fsState.cwd !== '') ? fsState.cwd : '');
          const data = await fsFetch(basePath);
          fsState.cwd = data.path;
          fsPathEl.textContent = data.path;
          renderFsList(data.entries || []);
        } catch (e) {
          alert(e.message || String(e));
        }
      }

      function renderFsList(entries) {
        fsState.selected = null;
        fsConfirmBtn.disabled = true;
        fsList.innerHTML = '';
        const frag = document.createDocumentFragment();
        for (const it of entries) {
          const row = document.createElement('div');
          const disabled = fsState.onlyDirs && it.type !== 'dir';
          row.className = 'fs-entry' + (disabled ? ' disabled' : '');
          row.dataset.path = it.path;
          row.dataset.type = it.type;
          row.innerHTML = `<div class="type">${it.type === 'dir' ? '📁' : '📄'}</div>
                           <div class="name">${it.name}</div>
                           <div class="meta">${it.type}</div>`;
          row.addEventListener('click', () => {
            if (it.type === 'dir') {
              // Select dir (for onlyDirs) or navigate with double click
              fsState.selected = { path: it.path, type: 'dir' };
              fsConfirmBtn.disabled = fsState.onlyDirs ? false : true;
            } else if (!disabled) {
              fsState.selected = { path: it.path, type: 'file' };
              fsConfirmBtn.disabled = false;
            }
          });
          row.addEventListener('dblclick', () => {
            if (it.type === 'dir') {
              loadFs(it.path);
            } else if (!disabled) {
              fsState.selected = { path: it.path, type: 'file' };
              applyFsSelection();
            }
          });
          frag.appendChild(row);
        }
        fsList.appendChild(frag);
        fsList.focus();
      }

      function applyFsSelection() {
        if (!fsState.selected || !fsState.targetInput) return;
        const v = fsState.selected.path;
        if (fsState.onlyDirs && fsState.selected.type !== 'dir') return;
        fsState.targetInput.value = v;
        closeFsModal();
        if (fsState.targetInput === inputPath) {
          loadLasListFromDir(v);
        }
      }

      // 逐个绑定（与之前一致）
      document.querySelectorAll('button.browse').forEach(btn => {
        btn.addEventListener('click', () => {
          const target = btn.dataset.target;
          const onlyDirs = btn.dataset.onlyDirs === 'true';
          const ext = btn.dataset.ext || null;
          openFsModal(target, onlyDirs, ext);
        });
      });

      fsUpBtn.addEventListener('click', () => {
        if (!fsState.cwd) return;
        const idx = fsState.cwd.lastIndexOf('/');
        const up = idx > 0 ? fsState.cwd.slice(0, idx) : '/';
        loadFs(up);
      });
      fsCancelBtn.addEventListener('click', closeFsModal);
      fsConfirmBtn.addEventListener('click', applyFsSelection);

      // 预览按钮
      previewLasBtn.addEventListener('click', refreshLasPreview);
      previewSelBtn.addEventListener('click', refreshSelPreview);

      // 当输入目录改动时，自动刷新下拉选项
      ['change','blur'].forEach(evt => inputPath.addEventListener(evt, () => {
        const dir = inputPath.value.trim();
        if (dir) loadLasListFromDir(dir);
      }));

      async function loadLasListFromDir(dir) {
        try {
          const resp = await fetch(`/fs/list?path=${encodeURIComponent(dir)}&only_dirs=false&ext=.las`);
          const data = await resp.json();
          const files = (data.entries || []).filter(e => e.type === 'file');
          populateLasSelect(lasSelectRaw, files);
          populateLasSelect(lasSelectSel, files);
        } catch (e) {
          console.error('加载 LAS 列表失败', e);
        }
      }

      function populateLasSelect(sel, files) {
        if (!sel) return;
        sel.innerHTML = '';
        const placeholder = document.createElement('option');
        placeholder.value = '';
        placeholder.textContent = '请选择 LAS 文件';
        sel.appendChild(placeholder);
        for (const f of files) {
          const opt = document.createElement('option');
          opt.value = f.path;
          opt.textContent = f.name;
          sel.appendChild(opt);
        }
      }

      // 初始加载一次（若默认目录存在）
      if (inputPath.value.trim()) {
        loadLasListFromDir(inputPath.value.trim());
      }

      // --- 俯视图缩放（以鼠标位置为中心） ---
      (function() {
        const ZOOM_MIN = 1;
        const ZOOM_MAX = 20;
        const ZOOM_BASE = 1.2; // 每个滚轮单位的缩放底数

        function applyTransform(img, state) {
          img.style.transform = `translate(${state.tx}px, ${state.ty}px) scale(${state.scale})`;
          img.style.cursor = state.scale > 1 ? 'zoom-out' : 'zoom-in';
        }

        function resetTransform(img) {
          const state = { scale: 1, tx: 0, ty: 0 };
          applyTransform(img, state);
          img._zoomState = state;
        }

        function attachZoom(img) {
          const container = img.closest('.pane-canvas');
          if (!container) return;
          resetTransform(img);

          // 每次图片加载后复位（刷新预览时）
          img.addEventListener('load', () => resetTransform(img));

          container.addEventListener('wheel', (e) => {
            e.preventDefault();
            const rect = container.getBoundingClientRect();
            const mx = e.clientX - rect.left;
            const my = e.clientY - rect.top;

            const st = img._zoomState || { scale: 1, tx: 0, ty: 0 };
            const prev = st.scale;
            // deltaY<0 放大、>0 缩小；按 100 像素平滑
            const factor = Math.pow(ZOOM_BASE, -e.deltaY / 100);
            let next = Math.max(ZOOM_MIN, Math.min(ZOOM_MAX, prev * factor));
            const k = next / prev;
            if (!isFinite(k) || k === 1) return;

            // 保持鼠标下的点位置不变：
            // tx2 = mx - (mx - tx) * k
            // ty2 = my - (my - ty) * k
            st.tx = mx - (mx - st.tx) * k;
            st.ty = my - (my - st.ty) * k;
            st.scale = next;
            applyTransform(img, st);
            img._zoomState = st;
          }, { passive: false });

          // 双击重置
          container.addEventListener('dblclick', () => resetTransform(img));
        }

        attachZoom(document.getElementById('img-las'));
        attachZoom(document.getElementById('img-sel'));
      })();

      // Esc to close modal
      window.addEventListener('keydown', (e) => {
        if (e.key === 'Escape') {
          if (!cpModal.classList.contains('hidden')) { closeCpModal(); return; }
          if (!fsModal.classList.contains('hidden')) closeFsModal();
        }
      });

      // --- Log explicit resizer handle ---
      const logEl = $('#log');
      const logResizer = document.querySelector('.log-resizer');
      let resizing = false, startY = 0, startH = 0;
      logResizer.addEventListener('mousedown', (e) => {
        resizing = true; startY = e.clientY; startH = logEl.getBoundingClientRect().height;
        document.body.style.userSelect = 'none';
      });
      window.addEventListener('mousemove', (e) => {
        if (!resizing) return;
        const dy = e.clientY - startY;
        const h = Math.max(160, startH + dy);
        logEl.style.height = h + 'px';
      });
      window.addEventListener('mouseup', () => { if (resizing) { resizing = false; document.body.style.userSelect = ''; } });
    </script>
  </body>
  </html>
