import os
import sys
import argparse
import time
import json
from dataclasses import dataclass
from typing import Optional, Tuple, List

import numpy as np
import laspy 
from scipy.spatial import cKDTree
import CSF  
import open3d as o3d  
from concurrent.futures import ThreadPoolExecutor
try:
    import pandas as pd
except ModuleNotFoundError:  # pragma: no cover - 运行时提示
    pd = None
try:
    import shapefile  # pyshp
except Exception:
    shapefile = None
# 不再导出 SHP，转为 LAS 带标签分类


# ======== Parameters ========
RADIUS = 0.3  
HEIGHT_P95_FLOOR = 0.0025
DENSITY_P95_FLOOR = 50
USE_LOG_DENSITY = False
RGB_LIMIT = 20
GB_RATIO = 1.2
CONTROL_POINT_RADIUS = 0.20  # meters


@dataclass
class ControlPoint:
    code: str
    x: float
    y: float
    z: float
    status: str


@dataclass
class ControlPointSet:
    total_rows: int
    points: List[ControlPoint]

    @property
    def count(self) -> int:
        return len(self.points)


def _is_missing(val) -> bool:
    if val is None:
        return True
    if isinstance(val, str):
        return val.strip() == ""
    if pd is not None:
        try:
            return bool(pd.isna(val))
        except Exception:
            pass
    return False


def _parse_float(val) -> Optional[float]:
    if _is_missing(val):
        return None
    if isinstance(val, (int, float, np.integer, np.floating)):
        if np.isnan(val):
            return None
        return float(val)
    try:
        text = str(val).strip().replace(",", "")
        if text == "":
            return None
        return float(text)
    except Exception:
        return None


def read_control_points_excel(path: str) -> ControlPointSet:
    if not path:
        raise ValueError("控制点路径为空")
    if not os.path.isfile(path):
        raise FileNotFoundError(f"控制点文件不存在: {path}")
    if pd is None:
        raise RuntimeError("读取控制点需要 pandas 支持，请安装 pandas>=1.0 和 xlrd>=2.0")

    try:
        df = pd.read_excel(path, header=None)
    except ImportError as e:
        raise RuntimeError("读取控制点需要安装 xlrd: pip install xlrd") from e
    except ValueError as e:
        # pandas 会在缺少引擎时抛 ValueError
        if "engine" in str(e).lower():
            raise RuntimeError("当前环境缺少 xlrd，无法读取 .xls 文件，请安装: pip install xlrd") from e
        raise

    values = df.to_numpy(dtype=object)
    total_rows = values.shape[0]

    points: List[ControlPoint] = []
    for row in values:
        if row.size == 0:
            continue
        code_raw = row[0] if row.size > 0 else None
        if _is_missing(code_raw):
            continue
        code = str(code_raw).strip()
        # 跳过标题行
        if code in {"代号", "编号", "点名", "控制点编号"}:
            continue

        y_val = row[1] if row.size > 1 else None
        x_val = row[2] if row.size > 2 else None
        z_val = row[3] if row.size > 3 else None
        status_val = row[4] if row.size > 4 else ""

        y = _parse_float(y_val)
        x = _parse_float(x_val)
        z = _parse_float(z_val)
        if y is None or x is None or z is None:
            continue  # 必须有完整坐标

        status = "" if _is_missing(status_val) else str(status_val).strip()
        points.append(ControlPoint(code=code, x=float(x), y=float(y), z=float(z), status=status))

    return ControlPointSet(total_rows=total_rows, points=points)


def linear_to_u8_array(arr: np.ndarray, vmin: float, vmax: float) -> np.ndarray:
    arr = np.asarray(arr, dtype=np.float64)
    if vmax <= vmin:
        return np.zeros(arr.shape, dtype=np.uint8)
    x = (arr - vmin) / (vmax - vmin)
    x = np.clip(x, 0.0, 1.0)
    return np.rint(x * 255.0).astype(np.uint8)


def log_to_u8_array(arr: np.ndarray, nmax: int) -> np.ndarray:
    arr = np.maximum(arr, 0)
    if nmax <= 0:
        return np.zeros(arr.shape, dtype=np.uint8)
    ln_den = np.log1p(float(nmax))
    if ln_den <= 0:
        return np.zeros(arr.shape, dtype=np.uint8)
    x = np.log1p(arr.astype(np.float64)) / ln_den
    x = np.clip(x, 0.0, 1.0)
    return np.rint(x * 255.0).astype(np.uint8)



def percentile_copy(arr: np.ndarray, p: float):
    if arr.size == 0:
        return 0
    p = min(max(p, 0.0), 1.0)
    return float(np.percentile(arr, p * 100.0))


def read_las_points_with_rgb(las_path: str) -> Tuple[np.ndarray, Optional[np.ndarray], object, object]:
    las = laspy.read(las_path)
    xyz = np.vstack((las.x, las.y, las.z)).T.astype(np.float64)

    colors = None
    if hasattr(las, "red") and hasattr(las, "green") and hasattr(las, "blue"):
        max_rgb = max(las.red.max(initial=0), las.green.max(initial=0), las.blue.max(initial=0))
        denom = 65535.0 if max_rgb and max_rgb > 255 else 255.0
        colors = np.vstack((las.red / denom, las.green / denom, las.blue / denom)).T
        colors = np.clip(colors, 0.0, 1.0).astype(np.float64)

    return xyz, colors, las.header, las.points


def write_las_from_subset(header, points, mask: np.ndarray, out_path: str):
    out = laspy.LasData(header)
    out.points = points[mask]
    out.write(out_path)


def las_to_pcd_with_rgb(las_file_path: str, pcd_file_path: str):
    las = laspy.read(las_file_path)
    pts = np.vstack((las.x, las.y, las.z)).T.astype(np.float64)
    if hasattr(las, "red") and hasattr(las, "green") and hasattr(las, "blue"):
        max_rgb = max(las.red.max(initial=0), las.green.max(initial=0), las.blue.max(initial=0))
        denom = 65535.0 if max_rgb and max_rgb > 255 else 255.0
        colors = np.vstack((las.red / denom, las.green / denom, las.blue / denom)).T
        colors = np.clip(colors, 0.0, 1.0).astype(np.float64)
    else:
        colors = np.ones((pts.shape[0], 3), dtype=np.float64)

    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(pts)
    pcd.colors = o3d.utility.Vector3dVector(colors)
    o3d.io.write_point_cloud(pcd_file_path, pcd, write_ascii=False, compressed=False)


def run_csf_ground_filter(xyz: np.ndarray, csf_params: dict) -> Tuple[np.ndarray, np.ndarray]:
    csf = CSF.CSF()
    csf.params.bSloopSmooth = bool(csf_params.get("bSloopSmooth", True))
    csf.params.cloth_resolution = float(csf_params.get("cloth_resolution", 2.0))
    csf.params.rigidness = int(csf_params.get("rigidness", 3))
    csf.params.class_threshold = float(csf_params.get("class_threshold", 0.25))
    csf.setPointCloud(xyz.astype(np.float64))

    ground = CSF.VecInt()
    non_ground = CSF.VecInt()
    csf.do_filtering(ground, non_ground)

    ground_idx = np.array(list(ground), dtype=np.int64)
    non_ground_idx = np.array(list(non_ground), dtype=np.int64)
    return ground_idx, non_ground_idx


def compute_normals_density_height(
    pcd: o3d.geometry.PointCloud,
    radius: float,
    device: str = "cpu",
    max_nn: int = 128,
    gpu_batch_size: Optional[int] = None,
) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """Compute normals, local density (neighbor count) and Z std within radius."""
    pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=radius, max_nn=max_nn))
    pcd.orient_normals_consistent_tangent_plane(10)

    pts = np.asarray(pcd.points)
    N = pts.shape[0]
    if N == 0:
        return (
            np.empty((0, 3), np.float64),
            np.empty((0,), np.int32),
            np.empty((0,), np.float64),
        )

    # GPU path for neighbor search and z-std
    if str(device).lower() == "gpu":
        try:
            import open3d.core as o3c
            # Robust CUDA detection across versions
            cuda_ok = False
            try:
                cuda_ok = bool(getattr(o3c.cuda, "is_available", lambda: False)())
            except Exception:
                try:
                    cuda_ok = getattr(o3c.cuda, "device_count", lambda: 0)() > 0
                except Exception:
                    cuda_ok = False
            if not cuda_ok:
                print("[GPU] 未检测到 CUDA 设备或未启用，回退 CPU")
                raise RuntimeError("CUDA not available")

            # Batch size for queries to limit peak memory
            if gpu_batch_size is None:
                gpu_batch_size = 200_000

            pts32 = pts.astype(np.float32, copy=False)
            dev = o3c.Device("CUDA:0")
            pts_ds = o3c.Tensor(pts32, device=dev)

            # Build index once
            nns = o3c.nns.NearestNeighborSearch(pts_ds)
            r = float(radius)
            k = int(max_nn)

            # 修复：hybrid_index 只接受 radius 参数
            has_hybrid = hasattr(nns, "hybrid_index") and hasattr(nns, "hybrid_search")
            if has_hybrid:
                # 只传递 radius，不传递 k
                nns.hybrid_index(r)
            else:
                print("[GPU] Tensor NNS 不支持 hybrid_index/search，使用 KNN+半径筛选回退")
                nns.knn_index()

            # Prepare outputs
            density = np.zeros(N, dtype=np.int32)
            height_std = np.zeros(N, dtype=np.float64)

            z = pts[:, 2].astype(np.float64, copy=False)
            z_pad = np.concatenate([z, [0.0]])  # pad at index N
            r2 = r * r

            # Iterate in batches to reduce memory pressure
            for start in range(0, N, int(gpu_batch_size)):
                end = min(N, start + int(gpu_batch_size))
                q = o3c.Tensor(pts32[start:end], device=dev)
                if has_hybrid:
                    # hybrid_search 仍然接受 radius 和 max_nn
                    inds, dists, counts = nns.hybrid_search(q, r, k)
                    inds_np = inds.cpu().numpy().astype(np.int64, copy=False)
                    counts_np = counts.cpu().numpy().astype(np.int32, copy=False)
                    valid = inds_np >= 0
                else:
                    inds, dists = nns.knn_search(q, k)
                    inds_np = inds.cpu().numpy().astype(np.int64, copy=False)
                    dists_np = dists.cpu().numpy().astype(np.float32, copy=False)
                    valid = (inds_np >= 0) & (dists_np <= r2)
                    counts_np = valid.sum(axis=1).astype(np.int32, copy=False)

                idx_safe = np.where(valid, inds_np, N)
                z_nb = z_pad[idx_safe]
                sum_z = (z_nb * valid).sum(axis=1)
                sum_z2 = ((z_nb ** 2) * valid).sum(axis=1)

                with np.errstate(invalid="ignore", divide="ignore"):
                    cnt = counts_np.astype(np.float64, copy=False)
                    mean = np.divide(sum_z, cnt, where=cnt > 0)
                    mean_sq = np.divide(sum_z2, cnt, where=cnt > 0)
                    var = np.maximum(mean_sq - mean * mean, 0.0)
                    std_part = np.sqrt(var)

                std_part[counts_np == 0] = 0.0
                height_std[start:end] = std_part
                density[start:end] = counts_np

            normals = np.asarray(pcd.normals)
            return normals, density, height_std
        except Exception as e:
            print(f"[GPU] 使用 Open3D Tensor 邻域搜索失败，改用 CPU：{type(e).__name__}: {e}")

    # CPU path (default)
    from scipy.spatial import cKDTree
    tree = cKDTree(pts)
    dists, idx = tree.query(pts, k=max_nn, distance_upper_bound=radius, workers=max(1, (os.cpu_count() or 2)//2))

    valid = np.isfinite(dists)
    counts = valid.sum(axis=1).astype(np.int32, copy=False)
    density = counts

    z = pts[:, 2].astype(np.float64, copy=False)
    z_pad = np.concatenate([z, [0.0]])
    idx_safe = np.where(valid, idx, N)
    z_nb = z_pad[idx_safe]

    sum_z = (z_nb * valid).sum(axis=1)
    sum_z2 = ((z_nb ** 2) * valid).sum(axis=1)

    with np.errstate(invalid="ignore", divide="ignore"):
        mean = np.divide(sum_z, counts, where=counts > 0)
        mean_sq = np.divide(sum_z2, counts, where=counts > 0)
        var = np.maximum(mean_sq - mean * mean, 0.0)
        height_std = np.sqrt(var)

    height_std[counts == 0] = 0.0
    normals = np.asarray(pcd.normals)
    return normals, density, height_std


def map_to_rgb_and_select(pts: np.ndarray,
                          normals: np.ndarray,
                          density: np.ndarray,
                          height_std: np.ndarray,
                          use_log_density: bool = USE_LOG_DENSITY,
                          rgb_limit: int = RGB_LIMIT,
                          avg_threshold: float = 0.50) -> Tuple[o3d.geometry.PointCloud, o3d.geometry.PointCloud, dict, np.ndarray]:
    h95 = percentile_copy(height_std, 0.95)
    d95 = percentile_copy(density, 0.95)
    height_clamp = max(HEIGHT_P95_FLOOR, h95)
    density_clamp = int(max(DENSITY_P95_FLOOR, d95))

    N = pts.shape[0]
    colors_u8 = np.zeros((N, 3), dtype=np.uint8)
    angles_deg = np.degrees(np.arccos(np.clip(np.abs(normals[:, 2]), -1.0, 1.0)))  # 0-180
    angles_deg = np.clip(angles_deg, 0.0, 90.0)

    r = linear_to_u8_array(height_std, 0.0, height_clamp)
    if use_log_density:
        g = log_to_u8_array(density, density_clamp)
    else:
        g = linear_to_u8_array(density.astype(np.float64), 0.0, float(density_clamp))
    b = linear_to_u8_array(angles_deg, 0.0, 90.0)

    colors_u8[:, 0] = r
    colors_u8[:, 1] = g
    colors_u8[:, 2] = b

    colored = o3d.geometry.PointCloud()
    colored.points = o3d.utility.Vector3dVector(pts)
    colored.colors = o3d.utility.Vector3dVector(colors_u8.astype(np.float64) / 255.0)

    # Selection by average threshold (single value)
    rgb_valid = (r >= rgb_limit) & (g >= rgb_limit) & (b >= rgb_limit)
    avg_score = (r.astype(np.float64) + g.astype(np.float64) + b.astype(np.float64)) / (3.0 * 255.0)

    thr = float(avg_threshold)
    mask = rgb_valid & (avg_score >= thr)
    selection = o3d.geometry.PointCloud()
    selection.points = o3d.utility.Vector3dVector(pts[mask])
    selection.colors = o3d.utility.Vector3dVector((colors_u8[mask].astype(np.float64) / 255.0))

    stats = {
        "h95": float(h95),
        "height_clamp": float(height_clamp),
        "d95": int(d95),
        "density_clamp": int(density_clamp),
    }
    return colored, selection, stats, mask


def process_single_point_cloud(input_path: str,
                               output_dir: str,
                               radius: float,
                               device: str = "cpu",
                               gb_enable: bool = True,
                               gb_threshold: float = GB_RATIO,
                               rgb_limit: int = RGB_LIMIT,
                               avg_threshold: float = 0.50,
                               control_points_path: Optional[str] = None,
                               sample_mut_m: float = 10.0,
                               sample_ground_m: float = 20.0,
                               merge_excl_m: float = 10.0):
    os.makedirs(output_dir, exist_ok=True)
    base = os.path.splitext(os.path.basename(input_path))[0]

    cp_set: Optional[ControlPointSet] = None
    if control_points_path:
        try:
            cp_set = read_control_points_excel(control_points_path)
        except Exception as exc:
            raise RuntimeError(f"读取控制点失败: {exc}") from exc
        print(f"[控制点] 从 {control_points_path} 读取 {cp_set.count} / {cp_set.total_rows} 行有效控制点")

    xyz, colors, header, points = read_las_points_with_rgb(input_path)

    origin_offset = xyz[0].copy()
    xyz_proc = xyz - origin_offset[None, :]
    print(f"[坐标归一] 已应用偏移 origin={origin_offset}")

    cp_global = None
    cp_local = None
    cp_codes: List[str] = []
    cp_status: List[str] = []
    if cp_set and cp_set.count > 0:
        cp_global = np.array([[p.x, p.y, p.z] for p in cp_set.points], dtype=np.float64)
        cp_local = cp_global - origin_offset[None, :]
        cp_codes = [p.code for p in cp_set.points]
        cp_status = [p.status for p in cp_set.points]

    # RGB G/B 比值过滤（可选）
    normal_mask = np.ones(xyz_proc.shape[0], dtype=bool)
    if gb_enable and (colors is not None) and hasattr(points, "blue") and hasattr(points, "green"):
        blue = points.blue
        green = points.green
        blue_safe = np.where(blue == 0, 1, blue)
        gb_ratio = green / blue_safe
        high_ratio_mask = gb_ratio > float(gb_threshold)
        normal_mask = ~high_ratio_mask
        removed = int(high_ratio_mask.sum())
        kept = int(normal_mask.sum())
        if removed > 0:
            high_ratio_path = os.path.join(output_dir, f"{base}_high_gb_ratio.las")
            # write_las_from_subset(header, points, high_ratio_mask, high_ratio_path)
        print(f"[RGB滤波] 已完成：剔除 {removed} 点，保留 {kept} 点，阈值 GB_RATIO={gb_threshold}")
    else:
        print("[RGB滤波] 已跳过或缺少颜色通道")

    xyz_filtered = xyz_proc[normal_mask]
    pts_for_processing = xyz_filtered

    # CSF 地面过滤
    ground_idx, non_ground_idx = run_csf_ground_filter(
        pts_for_processing,
        csf_params=dict(bSloopSmooth=True, cloth_resolution=2.0, rigidness=3, class_threshold=0.25),
    )

    ground_mask_full = normal_mask.copy()
    ground_mask_full[normal_mask] = False
    ground_mask_filtered = np.zeros(pts_for_processing.shape[0], dtype=bool)
    ground_mask_filtered[ground_idx] = True
    ground_mask_full[np.where(normal_mask)[0]] = ground_mask_filtered

    csf_out_path = os.path.join(output_dir, f"{base}_csf.las")
    # write_las_from_subset(header, points, ground_mask_full, csf_out_path)
    print(f"[CSF] 已完成：地面 {len(ground_idx)} 点，非地面 {len(non_ground_idx)} 点，输出 {csf_out_path}")

    pts_for_processing = pts_for_processing[ground_idx]
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(pts_for_processing)

    # 属性计算：法向、密度、z标准差
    normals, density, height_std = compute_normals_density_height(pcd, radius, device=device)
    print(f"[属性计算] 已完成：N={len(pts_for_processing)}，radius={radius}，device={device}")

    # RGB映射 + 多阈值选择
    colored, selection, stats, selection_mask = map_to_rgb_and_select(
        pts_for_processing, normals, density, height_std,
        use_log_density=USE_LOG_DENSITY, rgb_limit=int(rgb_limit), avg_threshold=float(avg_threshold)
    )

    def _with_offset(pc: o3d.geometry.PointCloud) -> o3d.geometry.PointCloud:
        out = o3d.geometry.PointCloud()
        pts = np.asarray(pc.points)
        out.points = o3d.utility.Vector3dVector(pts + origin_offset[None, :])
        out.colors = pc.colors
        return out

    rgb_path = os.path.join(output_dir, f"{base}_rgb.pcd")
    # o3d.io.write_point_cloud(rgb_path, _with_offset(colored), write_ascii=False, compressed=False)

    thr_int = int(round(float(avg_threshold) * 100.0))
    out_sel = os.path.join(output_dir, f"{base}_avgT{thr_int}.pcd")
    o3d.io.write_point_cloud(out_sel, _with_offset(selection), write_ascii=False, compressed=False)

    print(f"P95(Δh)={stats['h95']:.6f} clamp={stats['height_clamp']:.6f} | P95(density)={stats['d95']} clamp={stats['density_clamp']}")
    print(f"Saved colored: {rgb_path}")
    print(f"Saved selection avg>={float(avg_threshold):.2f}: {out_sel}")

    remaining_mask = ~selection_mask
    remaining_pts = pts_for_processing[remaining_mask]
    remaining_pcd = o3d.geometry.PointCloud()
    if remaining_pts.size > 0:
        remaining_pcd.points = o3d.utility.Vector3dVector(remaining_pts)
        rem_cols = np.full((remaining_pts.shape[0], 3), 0.65, dtype=np.float64)
        remaining_pcd.colors = o3d.utility.Vector3dVector(rem_cols)
    else:
        remaining_pcd.points = o3d.utility.Vector3dVector(np.empty((0, 3), dtype=np.float64))
        remaining_pcd.colors = o3d.utility.Vector3dVector(np.empty((0, 3), dtype=np.float64))

    remaining_out = os.path.join(output_dir, f"{base}_ground_remaining.pcd")
    # o3d.io.write_point_cloud(remaining_out, _with_offset(remaining_pcd), write_ascii=False, compressed=False)
    print(f"[控制点] 剩余地面点云 {remaining_pts.shape[0]} 点，输出 {remaining_out}")

    # 组合：突变点 + 剩余地面点，导出为一个带标签的 LAS（classification 字段作为 label）
    # 构建原始索引映射：selection / remaining -> 原始 LAS 索引
    N0 = xyz.shape[0]
    idx_normal = np.flatnonzero(normal_mask)
    if idx_normal.size and ground_idx.size:
        orig_ground_idx = idx_normal[ground_idx]
    else:
        orig_ground_idx = np.empty((0,), dtype=np.int64)

    sel_idx = orig_ground_idx[selection_mask] if orig_ground_idx.size else np.empty((0,), dtype=np.int64)
    rem_idx = orig_ground_idx[~selection_mask] if orig_ground_idx.size else np.empty((0,), dtype=np.int64)

    all_mask = np.zeros(N0, dtype=bool)
    if sel_idx.size:
        all_mask[sel_idx] = True
    if rem_idx.size:
        all_mask[rem_idx] = True

    total_n = int(all_mask.sum())
    if total_n > 0:
        # 0=地面剩余点, 1=突变点
        label_full = np.zeros(N0, dtype=np.uint8)
        if sel_idx.size:
            label_full[sel_idx] = 1

        try:
            out = laspy.LasData(header)
            out.points = points[all_mask]
            labels_sub = label_full[all_mask]
            dim_names = set(getattr(out.point_format, 'dimension_names', []) or [])
            if 'classification' in dim_names:
                out.classification = labels_sub
                label_field = 'classification'
            elif 'user_data' in dim_names:
                out.user_data = labels_sub
                label_field = 'user_data'
            else:
                label_field = None
                print("[警告] 点格式不含 classification/user_data，无法写入标签字段，仅写坐标/原属性")

            las_out_path = os.path.join(output_dir, f"{base}_labeled.las")
            out.write(las_out_path)
            if label_field:
                print(f"[导出] 合并 LAS 共 {total_n} 点：{label_field}=label (0=地面,1=突变) -> {las_out_path}")
            else:
                print(f"[导出] 合并 LAS 共 {total_n} 点（未写入标签字段）-> {las_out_path}")
        except Exception as e:
            print(f"[错误] 写入 LAS 失败：{type(e).__name__}: {e}")

    # 采样抽稀：突变点与地面剩余点分别按网格间隔采样，并按排斥半径剔除相邻地面点
    try:
        from scipy.spatial import cKDTree  # 可选，加速用
    except Exception:
        cKDTree = None

    def voxel_sample_xy(pts_xy: np.ndarray, spacing: float) -> np.ndarray:
        """
        随机顺序的贪心采样：选出子集，使任意两点欧氏距离 >= spacing。
        - 不采用网格分组
        - 若可用 SciPy，则用 cKDTree 加速；否则退化为纯 NumPy
        返回：满足最小间距约束的原始索引（升序）
        """
        # 基本检查
        if pts_xy.size == 0:
            return np.empty((0,), dtype=np.int64)
        if spacing <= 0:
            return np.arange(pts_xy.shape[0], dtype=np.int64)
        if pts_xy.ndim != 2 or pts_xy.shape[1] != 2:
            raise ValueError("pts_xy 应为 (N, 2) 的二维数组")

        N = pts_xy.shape[0]
        r = float(spacing)
        r2 = r * r

        # 随机顺序（若需可复现，可在函数外设置全局随机种子）
        rng = np.random.default_rng()
        order = rng.permutation(N)

        # SciPy 加速版（推荐）
        if cKDTree is not None:
            keep = []
            recent_pts = []         # 自上次建树后新增点（批量向量化检查）
            tree = None
            rebuild_every = 512     # 每加入这么多个点重建一次 KD 树（128~1024 之间可调）

            for idx in order:
                p = pts_xy[idx]
                accept = True

                # 1) 用 KD 树查半径内是否已有点
                if tree is not None:
                    if len(tree.query_ball_point(p, r)) > 0:
                        accept = False

                # 2) 与“最近新增未入树”的小批量做精确检查
                if accept and recent_pts:
                    q = np.vstack(recent_pts)  # (K,2)
                    if np.min((q[:, 0] - p[0])**2 + (q[:, 1] - p[1])**2) < r2:
                        accept = False

                if not accept:
                    continue

                # 接受该点
                keep.append(idx)
                recent_pts.append(p)

                # 达到阈值后重建 KD 树
                if len(recent_pts) >= rebuild_every:
                    tree = cKDTree(pts_xy[np.array(keep, dtype=np.int64)])
                    recent_pts = []

            keep = np.array(keep, dtype=np.int64)
            keep.sort()
            return keep

        # 纯 NumPy 版（无 SciPy 时使用）
        keep = []
        for idx in order:
            p = pts_xy[idx]
            if keep:
                q = pts_xy[np.array(keep, dtype=np.int64)]
                if np.min((q[:, 0] - p[0])**2 + (q[:, 1] - p[1])**2) < r2:
                    continue
            keep.append(idx)

        keep = np.array(keep, dtype=np.int64)
        keep.sort()
        return keep

    # 本地坐标（去掉 origin_offset）下进行 XY 采样
    mut_local = pts_for_processing[selection_mask]
    grd_local = remaining_pts
    mut_idx = voxel_sample_xy(mut_local[:, :2], float(sample_mut_m))
    grd_idx = voxel_sample_xy(grd_local[:, :2], float(sample_ground_m))

    mut_s = mut_local[mut_idx]
    grd_s = grd_local[grd_idx]

    # 从地面采样中剔除距离突变采样点 merge_excl_m 内的点（XY）
    if mut_s.size and grd_s.size and merge_excl_m > 0:
        mxy = mut_s[:, :2]
        gxy = grd_s[:, :2]
        tree = cKDTree(mxy)
        hit = tree.query_ball_point(gxy, r=float(merge_excl_m))
        keep_mask = np.array([len(h) == 0 for h in hit], dtype=bool)
        grd_s = grd_s[keep_mask]

    # 输出采样结果（加回原点偏移），各自与合并
    def _to_pcd(pts_local: np.ndarray, color: tuple[float, float, float]):
        pc = o3d.geometry.PointCloud()
        if pts_local.size:
            pc.points = o3d.utility.Vector3dVector(pts_local + origin_offset[None, :])
            col = np.tile(np.asarray(color, dtype=np.float64)[None, :], (pts_local.shape[0], 1))
            pc.colors = o3d.utility.Vector3dVector(col)
        else:
            pc.points = o3d.utility.Vector3dVector(np.empty((0, 3), dtype=np.float64))
            pc.colors = o3d.utility.Vector3dVector(np.empty((0, 3), dtype=np.float64))
        return pc

    # 采样点导出为 SHP（含类别字段 cls: 1=突变, 0=地面）
    def write_samples_to_shp(mut_local_pts: np.ndarray, grd_local_pts: np.ndarray, base_name: str):
        if shapefile is None:
            print("[采样][SHP] 未安装 pyshp 库，跳过导出（pip install pyshp）")
            return
        try:
            # 组合到一个 SHP，type 区分 1=突变，0=地面
            pts_list = []
            if mut_local_pts is not None and mut_local_pts.size:
                xyz = mut_local_pts + origin_offset[None, :]
                for p in xyz:
                    pts_list.append((float(p[0]), float(p[1]), float(p[2]), 1))
            if grd_local_pts is not None and grd_local_pts.size:
                xyz = grd_local_pts + origin_offset[None, :]
                for p in xyz:
                    pts_list.append((float(p[0]), float(p[1]), float(p[2]), 0))

            shp_path = os.path.join(output_dir, f"{base_name}_sample_points.shp")
            w = shapefile.Writer(shp_path, shapeType=shapefile.POINTZ)
            # 字段名 <=10 字符（DBF 限制）
            w.field('cls', 'N', size=1, decimal=0)  # 1=突变,0=地面
            for x, y, z, cls in pts_list:
                w.pointz(x, y, z)
                w.record(cls=int(cls))
            w.close()
            # 可选：不写 .prj（坐标系未知），如需可在此处创建 .prj
            print(f"[采样][SHP] 导出 {len(pts_list)} 点 -> {shp_path}")
        except Exception as e:
            print(f"[采样][SHP] 导出失败：{type(e).__name__}: {e}")

    write_samples_to_shp(mut_s, grd_s, base)

    cp_summary_payload = None
    if cp_set and cp_local is not None:
        cp_summary_payload = {
            "total_rows": cp_set.total_rows,
            "valid_points": cp_set.count,
            "in_range": 0,
            "radius_m": CONTROL_POINT_RADIUS,
            "points": [],
        }

        if remaining_pts.size == 0:
            print("[控制点] 剩余点云为空，无法匹配控制点")
        else:
            min_xy = remaining_pts[:, :2].min(axis=0)
            max_xy = remaining_pts[:, :2].max(axis=0)
            inside_mask = (
                (cp_local[:, 0] >= min_xy[0]) & (cp_local[:, 0] <= max_xy[0]) &
                (cp_local[:, 1] >= min_xy[1]) & (cp_local[:, 1] <= max_xy[1])
            )
            inside_indices = np.flatnonzero(inside_mask)
            cp_summary_payload["in_range"] = int(inside_indices.size)
            print(f"[控制点] XY 范围 X[{min_xy[0]:.3f},{max_xy[0]:.3f}] Y[{min_xy[1]:.3f},{max_xy[1]:.3f}] 内控制点 {inside_indices.size} / {cp_set.count}")

            if inside_indices.size == 0:
                print("[控制点] XY 范围内没有控制点")
            else:
                tree = cKDTree(remaining_pts) if remaining_pts.size > 0 else None
                radius = CONTROL_POINT_RADIUS
                for idx in inside_indices:
                    neighbors = tree.query_ball_point(cp_local[idx], r=radius) if tree is not None else []
                    neighbor_count = len(neighbors)
                    if neighbor_count > 0:
                        nb_pts = remaining_pts[neighbors]
                        zloc = nb_pts[:, 2].astype(np.float64)
                        # 统计（局部 -> 全局坐标）：P90、中位、均值、最大、最小
                        local_p90 = float(np.percentile(zloc, 90))
                        local_p50 = float(np.median(zloc))
                        local_mean = float(np.mean(zloc))
                        local_zmax = float(np.max(zloc))
                        local_zmin = float(np.min(zloc))

                        global_p90 = local_p90 + origin_offset[2]
                        global_p50 = local_p50 + origin_offset[2]
                        global_mean = local_mean + origin_offset[2]
                        global_zmax = local_zmax + origin_offset[2]
                        global_zmin = local_zmin + origin_offset[2]

                        delta = global_mean - cp_global[idx, 2]
                        print(
                            f"[控制点] {cp_codes[idx]}: 半径{radius:.2f}m内 {neighbor_count} 点, "
                            f"P90={global_p90:.3f}, 中位={global_p50:.3f}, 均值={global_mean:.3f}, "
                            f"最高={global_zmax:.3f}, 最低={global_zmin:.3f}, Δh={delta*100:.1f}cm"
                        )
                    else:
                        local_p90 = None
                        global_p90 = None
                        global_p50 = None
                        global_mean = None
                        global_zmax = None
                        global_zmin = None
                        delta = None
                        print(f"[控制点] {cp_codes[idx]}: 半径{radius:.2f}m内无点")

                    cp_summary_payload["points"].append({
                        "code": cp_codes[idx],
                        "status": cp_status[idx],
                        "x": cp_global[idx, 0],
                        "y": cp_global[idx, 1],
                        "z": cp_global[idx, 2],
                        "neighbor_count": neighbor_count,
                        "p90": global_p90,
                        "p50": global_p50,
                        "mean": global_mean,
                        "zmax": global_zmax,
                        "zmin": global_zmin,
                        "delta": delta,
                    })

        print(f"[控制点] 汇总: 有效 {cp_set.count} 个, 范围内 {cp_summary_payload['in_range']} 个")
        print("CONTROL_POINT_SUMMARY " + json.dumps(cp_summary_payload, ensure_ascii=False))


def main():
    parser = argparse.ArgumentParser(description="Merge CSF ground extraction and RGB mapping for a single point cloud (LAS/PCD)")
    parser.add_argument("--input", required=True, help="Path to input .las or .pcd")
    parser.add_argument("--output_dir", required=True, help="Directory to save outputs")
    parser.add_argument("--device", choices=["cpu", "gpu"], default="cpu", help="Attribute compute device: cpu or gpu (default: cpu)")
    parser.add_argument("--gb_enable", action="store_true", help="Enable GB ratio filtering")
    parser.add_argument("--gb_threshold", type=float, default=GB_RATIO, help="GB ratio threshold (default: 1.2)")
    parser.add_argument("--avg_threshold", type=float, default=0.20, help="Average RGB score threshold (0-1)")
    parser.add_argument("--rgb_limit", type=int, default=RGB_LIMIT, help="Per-channel minimum (0-255)")
    parser.add_argument("--control_points", type=str, default=None, help="Control point spreadsheet (.xls/.xlsx)")
    parser.add_argument("--sample_mut_m", type=float, default=10.0, help="Sampling interval for mutation points in meters (default: 10)")
    parser.add_argument("--sample_ground_m", type=float, default=30.0, help="Sampling interval for ground points in meters (default: 30)")
    parser.add_argument("--merge_excl_m", type=float, default=10.0, help="Exclusion radius: drop ground samples within this distance from mutation samples (default: 10)")
    args = parser.parse_args()

    t0 = time.time()
    process_single_point_cloud(
        args.input,
        args.output_dir,
        radius=RADIUS,
        device=args.device,
        gb_enable=bool(args.gb_enable),
        gb_threshold=float(args.gb_threshold),
        rgb_limit=int(args.rgb_limit),
        avg_threshold=float(args.avg_threshold),
        control_points_path=args.control_points,
        sample_mut_m=float(args.sample_mut_m),
        sample_ground_m=float(args.sample_ground_m),
        merge_excl_m=float(args.merge_excl_m),
    )
    print(f"Done in {time.time() - t0:.2f}s")


if __name__ == "__main__":
    main()
