import os
import sys
import subprocess
from pathlib import Path
from flask import Flask, Response, render_template, request, jsonify, send_file
import io
import math
import json
import numpy as np
import laspy
import open3d as o3d
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
try:
    from PIL import Image
except Exception:
    Image = None


app = Flask(__name__, template_folder="templates", static_folder="static")


@app.route("/")
def index():
    return render_template("index.html")


@app.route("/run")
def run():
    input_path = request.args.get("input_path", "").strip()
    output_dir = request.args.get("output_dir", "").strip()
    device = request.args.get("device", "cpu").strip().lower() or "cpu"
    gb_enable = request.args.get("gb_enable", "false").lower() == "true"
    gb_threshold = request.args.get("gb_threshold", "1.2")
    avg_threshold = request.args.get("avg_threshold", "0.20")
    rgb_limit = request.args.get("rgb_limit", "20")
    control_path = request.args.get("control_path", "").strip()
    sample_mut_m = request.args.get("sample_mut_m", "10")
    sample_ground_m = request.args.get("sample_ground_m", "20")
    merge_excl_m = request.args.get("merge_excl_m", "10")

    # Resolve paths relative to where the server runs
    repo_root = Path(__file__).resolve().parents[1]
    script_path = str(repo_root / "single_csf_segment.py")

    def list_las_files(p: str) -> list[str]:
        base = Path(p)
        if base.is_dir():
            return [str(x) for x in sorted(base.glob("*.las"))]
        if base.is_file() and base.suffix.lower() == ".las":
            return [str(base)]
        return []

    las_files = list_las_files(input_path)

    def generate():
        nonlocal las_files
        yield f"data: ▶️ 启动任务...\n\n"
        if not las_files:
            yield f"event: error\n" + f"data: ❌ 未找到任何 .las 文件: {input_path}\n\n"
            return
        yield f"data: 输入目录: {input_path}\n\n"
        yield f"data: 输出: {output_dir}\n\n"
        if control_path:
            yield f"data: 控制点: {control_path}\n\n"

        # Prepare per-file commands
        def make_args(infile: str) -> list[str]:
            args = [sys.executable, script_path, "--input", infile, "--output_dir", output_dir,
                    "--device", ("gpu" if device == "gpu" else "cpu"),
                    "--gb_threshold", gb_threshold, "--avg_threshold", avg_threshold, "--rgb_limit", rgb_limit]
            if gb_enable:
                args.append("--gb_enable")
            if control_path:
                args.extend(["--control_points", control_path])
            # sampling params
            args.extend(["--sample_mut_m", str(sample_mut_m),
                        "--sample_ground_m", str(sample_ground_m),
                        "--merge_excl_m", str(merge_excl_m)])
            return args

        try:
            import selectors
            sel = selectors.DefaultSelector()

            pending = list(las_files)
            running: dict[int, tuple[str, subprocess.Popen]] = {}
            started = 0
            finished = 0
            max_parallel = min(4, len(pending))
            cp_all: list[dict] = []  # accumulate control point summaries across LAS

            def start_one(lf: str):
                nonlocal started
                base = Path(lf).name
                args = make_args(lf)
                proc = subprocess.Popen(
                    args,
                    cwd=str(repo_root),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    text=True,
                    bufsize=1,
                )
                assert proc.stdout is not None
                sel.register(proc.stdout, selectors.EVENT_READ, data=(base, proc))
                running[id(proc.stdout)] = (base, proc)
                started += 1
                return base

            # Start initial batch
            for _ in range(max_parallel):
                if not pending:
                    break
                base = start_one(pending.pop(0))
                yield f"data: ▶️ 启动: {base}\n\n"

            # Multiplex outputs and refill up to max_parallel
            # while finished < (len(pending) + len(running)):
                
            total = len(las_files)
            while finished < total:

                events = sel.select(timeout=0.2)
                if not events:
                    # No new output; also check for exits to start refills
                    to_refill = []
                    for _, (base, proc) in list(running.items()):
                        rc = proc.poll()
                        if rc is not None and proc.stdout is not None:
                            try:
                                sel.unregister(proc.stdout)
                            except Exception:
                                pass
                            try:
                                proc.stdout.close()
                            except Exception:
                                pass
                            running.pop(id(proc.stdout), None)
                            finished += 1
                            yield f"data: ✅ 完成: {base} (exit={rc})\n\n"
                            to_refill.append(1)
                    for _ in to_refill:
                        if pending:
                            base = start_one(pending.pop(0))
                            yield f"data: ▶️ 启动: {base}\n\n"
                    continue

                for key, _ in events:
                    stream = key.fileobj
                    base, proc = key.data
                    line = stream.readline()
                    if not line:
                        # EOF: mark finished and maybe start another
                        try:
                            sel.unregister(stream)
                        except Exception:
                            pass
                        try:
                            stream.close()
                        except Exception:
                            pass
                        running.pop(id(stream), None)
                        rc = proc.poll()
                        if rc is None:
                            try:
                                rc = proc.wait(timeout=0.5)
                            except Exception:
                                rc = None
                        finished += 1
                        yield f"data: ✅ 完成: {base} (exit={rc})\n\n"
                        if pending:
                            nb = start_one(pending.pop(0))
                            yield f"data: ▶️ 启动: {nb}\n\n"
                        continue
                    line = line.rstrip("\n\r")
                    if line.startswith("CONTROL_POINT_SUMMARY "):
                        # Parse and tag with LAS filename for frontend aggregation
                        try:
                            payload = line[len("CONTROL_POINT_SUMMARY "):]
                            data_obj = json.loads(payload)
                        except Exception:
                            data_obj = None
                        tagged = {"las": base, "summary": data_obj}
                        cp_all.append(tagged)
                        yield f"data: CONTROL_POINT_SUMMARY {json.dumps(tagged, ensure_ascii=False)}\n\n"
                    else:
                        yield f"data: [{base}] {line}\n\n"

            # Ensure all joined
            for _, proc in list(running.values()):
                try:
                    proc.wait(timeout=1.0)
                except Exception:
                    pass

            # Write aggregated control point summary to text if any
            try:
                if cp_all:
                    os.makedirs(output_dir, exist_ok=True)
                    out_txt = Path(output_dir) / "control_point_summary.txt"
                    with open(out_txt, "w", encoding="utf-8") as f:
                        f.write("LAS\tCode\tStatus\tX\tY\tZ\tNeighborCount\tP90\tP50\tMean\tZmax\tZmin\tDelta\n")
                        for entry in cp_all:
                            las_name = entry.get("las") or ""
                            summ = entry.get("summary") or {}
                            points = summ.get("points") or []
                            for pt in points:
                                f.write(
                                    f"{las_name}\t{pt.get('code','')}\t{pt.get('status','')}\t{pt.get('x','')}\t{pt.get('y','')}\t{pt.get('z','')}\t{pt.get('neighbor_count','')}\t{pt.get('p90','')}\t{pt.get('p50','')}\t{pt.get('mean','')}\t{pt.get('zmax','')}\t{pt.get('zmin','')}\t{pt.get('delta','')}\n"
                                )
                    yield f"data: 📄 控制点汇总已保存: {out_txt}\n\n"
            except Exception as _e:
                # Failing to write summary should not abort the run
                yield f"data: ⚠️ 写入控制点汇总失败: {_e}\n\n"

            total = started
            yield f"data: ✅ 全部任务结束，共 {total} 个 LAS\n\n"
            yield "event: done\n" + "data: done\n\n"
        except Exception as e:
            yield f"event: error\n" + f"data: ❌ {type(e).__name__}: {e}\n\n"

    return Response(generate(), mimetype="text/event-stream")


def _read_las_xyz_rgb(path: str):
    las = laspy.read(path)
    xyz = np.vstack((las.x, las.y, las.z)).T.astype(np.float64)
    colors = None
    if hasattr(las, "red") and hasattr(las, "green") and hasattr(las, "blue"):
        max_rgb = max(las.red.max(initial=0), las.green.max(initial=0), las.blue.max(initial=0))
        denom = 65535.0 if max_rgb and max_rgb > 255 else 255.0
        colors = np.vstack((las.red / denom, las.green / denom, las.blue / denom)).T
        colors = np.clip(colors, 0.0, 1.0).astype(np.float64)
    return xyz, colors


def _read_pcd_xyz_rgb(path: str):
    pcd = o3d.io.read_point_cloud(path)
    pts = np.asarray(pcd.points, dtype=np.float64)
    cols = np.asarray(pcd.colors, dtype=np.float64) if pcd.has_colors() else None
    if cols is not None and cols.size == 0:
        cols = None
    return pts, cols


def _group_min_by_key(keys: np.ndarray, vals: np.ndarray) -> tuple[np.ndarray, np.ndarray]:
    if keys.size == 0:
        return keys, vals
    order = np.argsort(keys, kind='mergesort')  # stable
    ks = keys[order]
    vs = vals[order]
    # Find group starts
    starts = np.empty(ks.shape[0], dtype=bool)
    starts[0] = True
    starts[1:] = ks[1:] != ks[:-1]
    idx = np.flatnonzero(starts)
    mins = np.minimum.reduceat(vs, idx)
    uniq = ks[idx]
    return uniq, mins


def _first_per_pixel_parallel(r: np.ndarray, orig_idx: np.ndarray, workers: int) -> tuple[np.ndarray, np.ndarray]:
    # Compute per-pixel minimal original index in parallel by chunking the inputs
    n = r.shape[0]
    if workers <= 1 or n < 200_000:  # small arrays faster single-threaded
        return _group_min_by_key(r, orig_idx)

    workers = max(1, int(workers))
    chunk = int(math.ceil(n / workers))
    tasks = []
    with ThreadPoolExecutor(max_workers=workers) as ex:
        for s in range(0, n, chunk):
            e = min(n, s + chunk)
            rc = r[s:e]
            ic = orig_idx[s:e]
            tasks.append(ex.submit(_group_min_by_key, rc, ic))

        parts_r = []
        parts_min = []
        for t in tasks:
            ur, mi = t.result()
            parts_r.append(ur)
            parts_min.append(mi)

    if not parts_r:
        return np.empty((0,), dtype=r.dtype), np.empty((0,), dtype=orig_idx.dtype)

    all_r = np.concatenate(parts_r)
    all_min = np.concatenate(parts_min)
    return _group_min_by_key(all_r, all_min)


def _project_topdown(xyz: np.ndarray, colors01: np.ndarray | None, pixel_size_m: float = 0.10,
                     target_size: int = 800, workers: int | None = None) -> bytes:
    """Return PNG bytes of a top-down projection at 10cm/pixel.
    - Fills first point per pixel.
    - Scales into a square canvas of target_size x target_size while preserving aspect ratio.
    - Background white.
    """
    if Image is None:
        raise RuntimeError("Pillow is required for image encoding, please `pip install pillow`.")

    if xyz.size == 0:
        # Return blank white square
        img = Image.new('RGB', (target_size, target_size), color=(255, 255, 255))
        buf = io.BytesIO()
        img.save(buf, format='PNG')
        return buf.getvalue()

    xy = xyz[:, :2]
    min_xy = xy.min(axis=0)
    max_xy = xy.max(axis=0)
    span = np.maximum(max_xy - min_xy, 1e-9)

    # Grid size in pixels (ceil to cover bounds)
    w = int(math.ceil(span[0] / pixel_size_m)) + 1
    h = int(math.ceil(span[1] / pixel_size_m)) + 1

    # Map to pixel indices; y inverted so larger y at top visually
    j = np.floor((xy[:, 0] - min_xy[0]) / pixel_size_m).astype(np.int64)
    i = np.floor((max_xy[1] - xy[:, 1]) / pixel_size_m).astype(np.int64)
    # Clamp just in case
    j = np.clip(j, 0, max(0, w - 1))
    i = np.clip(i, 0, max(0, h - 1))

    # First-hit per pixel: use raveled index and unique with return_index
    r = i * w + j
    # Keep minimal original index per pixel (first point semantics)
    orig_idx = np.arange(xyz.shape[0], dtype=np.int64)
    if workers is None:
        cpu = os.cpu_count() or 1
        workers = max(1, cpu // 2)
    uniq_r, first_idx = _first_per_pixel_parallel(r, orig_idx, workers)
    # Prepare image buffer (white background)
    img_arr = np.full((h, w, 3), 255, dtype=np.uint8)

    if colors01 is not None:
        cols = (np.clip(colors01, 0.0, 1.0) * 255.0 + 0.5).astype(np.uint8)
    else:
        # If no RGB, draw as black points
        cols = np.zeros((xyz.shape[0], 3), dtype=np.uint8)

    # Derive positions back from r to avoid reindexing i/j arrays
    fi_i = np.floor_divide(uniq_r, w)
    fi_j = np.remainder(uniq_r, w)
    img_arr[fi_i, fi_j, :] = cols[first_idx]

    # Resize to target square with aspect preserved
    src_img = Image.fromarray(img_arr, mode='RGB')
    # Compute scale to fit into target_size x target_size
    scale = min(target_size / max(1, w), target_size / max(1, h))
    new_w = max(1, int(round(w * scale)))
    new_h = max(1, int(round(h * scale)))
    resized = src_img.resize((new_w, new_h), resample=Image.NEAREST)

    canvas = Image.new('RGB', (target_size, target_size), color=(255, 255, 255))
    ox = (target_size - new_w) // 2
    oy = (target_size - new_h) // 2
    canvas.paste(resized, (ox, oy))

    buf = io.BytesIO()
    canvas.save(buf, format='PNG')
    return buf.getvalue()


@app.get("/preview/las")
def preview_las():
    input_path = request.args.get("input_path", "").strip()
    size = int(request.args.get("size", "800"))
    workers = request.args.get("workers", "")
    workers = int(workers) if workers.strip().isdigit() else None
    if not input_path:
        return jsonify({"error": "missing input_path"}), 400
    try:
        xyz, cols = _read_las_xyz_rgb(input_path)
        png = _project_topdown(xyz, cols, pixel_size_m=0.10, target_size=size, workers=workers)
        return Response(png, mimetype='image/png')
    except Exception as e:
        return jsonify({"error": f"{type(e).__name__}: {e}"}), 500


@app.get("/preview/selection")
def preview_selection():
    """Preview the extracted selection (mutation) points as a top-down image.
    If `las` is provided, restrict to that basename; otherwise falls back to newest *_avgT*.pcd.
    """
    output_dir = request.args.get("output_dir", "").strip()
    las_hint = request.args.get("las", "").strip()
    size = int(request.args.get("size", "800"))
    workers = request.args.get("workers", "")
    workers = int(workers) if workers.strip().isdigit() else None
    if not output_dir:
        return jsonify({"error": "missing output_dir"}), 400
    try:
        base_dir = Path(output_dir)
        if not base_dir.exists() or not base_dir.is_dir():
            return jsonify({"error": "output_dir not found"}), 400

        candidates: list[Path]
        if las_hint:
            base = os.path.splitext(os.path.basename(las_hint))[0]
            candidates = list(base_dir.glob(f"{base}_avgT*.pcd"))
            if not candidates:
                return jsonify({"error": f"no selection PCD for {base} (pattern {base}_avgT*.pcd)"}), 404
        else:
            candidates = list(base_dir.glob("*_avgT*.pcd"))
            if not candidates:
                return jsonify({"error": "no selection PCD found (pattern *_avgT*.pcd)"}), 404

        target = max(candidates, key=lambda p: p.stat().st_mtime)
        pts, cols = _read_pcd_xyz_rgb(str(target))
        png = _project_topdown(pts, cols, pixel_size_m=0.3, target_size=size, workers=workers)
        return Response(png, mimetype='image/png')
    except Exception as e:
        return jsonify({"error": f"{type(e).__name__}: {e}"}), 500


@app.get("/fs/list")
def fs_list():
    """List server-side filesystem entries for a given path.
    Optional query params:
      - path: base path to list; defaults to current working dir
      - only_dirs: 'true' to include only directories
      - ext: comma-separated whitelist of file extensions (e.g. '.las,.pcd')
    """
    raw_path = request.args.get("path", "").strip()
    only_dirs = request.args.get("only_dirs", "false").lower() == "true"
    ext_raw = request.args.get("ext", "").strip()
    ext_whitelist = None
    if ext_raw:
        ext_whitelist = {e.lower().strip() for e in ext_raw.split(",") if e.strip()}

    try:
        base = Path(raw_path).expanduser() if raw_path else Path.cwd()
        base = base.resolve()
        if not base.exists():
            base = base.parent if base.parent.exists() else Path("/")

        entries = []
        with os.scandir(base) as it:
            for e in it:
                try:
                    is_dir = e.is_dir(follow_symlinks=False)
                except Exception:
                    # Skip entries we cannot stat
                    continue

                if is_dir:
                    entries.append({
                        "name": e.name,
                        "path": str(base / e.name),
                        "type": "dir",
                    })
                elif not only_dirs:
                    if ext_whitelist:
                        _, ext = os.path.splitext(e.name)
                        if ext.lower() not in ext_whitelist:
                            continue
                    entries.append({
                        "name": e.name,
                        "path": str(base / e.name),
                        "type": "file",
                    })

        # Sort: dirs first, then files, both alphabetically
        entries.sort(key=lambda x: (0 if x["type"] == "dir" else 1, x["name"].lower()))
        return jsonify({"path": str(base), "entries": entries})
    except Exception as e:
        return jsonify({"error": f"{type(e).__name__}: {e}"}), 400


if __name__ == "__main__":
    # Host on 127.0.0.1 by default
    app.run(host="127.0.0.1", port=5000, debug=True, threaded=True)
