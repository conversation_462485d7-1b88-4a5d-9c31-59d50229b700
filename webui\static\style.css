@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap');

/* ================== SCI Palette ================== */
:root {
  /* 基底（冷白+理性灰） */
  --bg: #FAFBFE;          /* 冷白背景 */
  --panel: #ffffff;       /* 白色卡片 */
  --panel-2: #F7F9FD;     /* 轻微过渡白蓝 */
  --text: #0F172A;        /* 深灰蓝正文 */
  --muted: #6b7280;       /* 次要文字 */
  --border: #E7EAF2;      /* 更细更冷的边 */

  /* 品牌/功能色 */
  --primary:   #1E3A8A;   /* 深蓝（主按钮/标题） */
  --primary-2: #3B82F6;   /* 天蓝（hover/次强调） */
  --accent:    #FFF7D6;   /* 淡黄（提示/浅背景高亮） */
  --green:     #10b981;   /* 成功 */
  --danger:    #DC2626;   /* 危险/停止 */

  /* 细节统一 */
  --radius: 12px;
  --radius-sm: 10px;
  --shadow-sm: 0 4px 14px rgba(8,15,35,.06);
  --shadow-md: 0 10px 34px rgba(8,15,35,.10);

  --c-io:     #0B5BD3;  /* 输入/输出标签颜色（偏科研的蓝） */
  --c-param:  #0E7490;  /* 调参标签颜色（青蓝） */
  --c-log:    #334155
}

.label-io { color: var(--c-io); }

/* 参数类标签（含复选说明 & 滑块标题） */
.label-param { color: var(--c-param); }
#log { color: var(--c-log); }
label.label-param + .slider output { color: var(--c-param); }
/* ================ Base ================ */
* { box-sizing: border-box; }
html, body { height: 100%; }
body {
  margin: 0;
  background:
    /* 左上角淡黄光晕 */
    radial-gradient(1200px 700px at 20% 0%, rgba(255,247,214,.35) 0%, rgba(255,247,214,0) 70%),
    /* 右上角淡黄光晕 */
    radial-gradient(1200px 700px at 80% 0%, rgba(255,247,214,.35) 0%, rgba(255,247,214,0) 70%),
    /* 中央上方补充光晕，使过渡延续到中间 */
    radial-gradient(1400px 800px at 50% 10%, rgba(255,247,214,.28) 0%, rgba(255,247,214,0) 75%),
    /* 底层冷白过渡 */
    linear-gradient(180deg, #ffffff 0%, var(--bg) 100%);
  color: var(--text);
  font: 14px/1.6 'Inter', system-ui, -apple-system, Segoe UI, Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container { max-width: 1000px; margin: 24px auto; padding: 0 16px; }

header { margin-bottom: 16px; }
header h1 { margin: 0 0 6px; font-weight: 600; letter-spacing: 0.3px; color: var(--primary); }
.sub { margin: 0; color: var(--muted); }

/* ================ Card ================ */
.card {
  background: linear-gradient(180deg, var(--panel), var(--panel-2));
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 16px;
  box-shadow: var(--shadow-sm);
  margin-bottom: 16px;
}

/* ================ Form ================ */
label { display: block; color: var(--muted); margin-bottom: 6px; }

input[type="text"] {
  width: 100%;
  padding: 11px 12px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
  background: #ffffff;
  color: var(--text);
  outline: none;
  transition: border-color .18s ease, box-shadow .18s ease, background .18s ease;
}
input[type="text"]::placeholder { color: #98a3b6; }
input[type="text"]:hover { border-color: #E1E6F1; }
input[type="text"]:focus { border-color: var(--primary-2); box-shadow: 0 0 0 3px rgba(59,130,246,.18); }

.row { margin-bottom: 14px; }
.grid { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; }
.col { }

.checkbox { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; }
.checkbox input { width: 18px; height: 18px; accent-color: var(--primary-2); }

/* ================ Slider ================ */
.slider { display: grid; grid-template-columns: 1fr auto; align-items: center; gap: 10px; }
input[type=range] {
  width: 100%;
  appearance: none;
  height: 4px;
  border-radius: 999px;
  background: transparent; /* 由轨道伪元素绘制 */
  outline: none;
}
/* WebKit 轨道与已选中部分填充（左侧深蓝） */
input[type=range]::-webkit-slider-runnable-track {
  height: 4px;
  border-radius: 999px;
  background: linear-gradient(to right,
              var(--primary) 0%,
              var(--primary) var(--range-pct, 0%),
              #EDF1F8 var(--range-pct, 0%),
              #EDF1F8 100%);
}
/* Firefox 轨道与进度（左侧深蓝） */
input[type=range]::-moz-range-track {
  height: 4px;
  border-radius: 999px;
  background: #EDF1F8;
}
input[type=range]::-moz-range-progress {
  height: 4px;
  border-radius: 999px;
  background: var(--primary);
}
input[type=range]::-webkit-slider-thumb {
  appearance: none;
  width: 16px; height: 16px; border-radius: 50%;
  background: linear-gradient(180deg, var(--primary-2), #2563eb);
  box-shadow: 0 2px 6px rgba(59,130,246,.35);
  cursor: pointer;
  transition: transform .15s ease;
}
input[type=range]::-webkit-slider-thumb:hover { transform: scale(1.05); }
output { min-width: 48px; text-align: right; color: var(--text); font-variant-numeric: tabular-nums; }

/* ================ Actions & Buttons ================ */
.actions { display: flex; gap: 10px; justify-content: flex-end; }

/* 默认按钮：科研工具常用的“白底+细描边”，克制、不喧宾夺主 */
button {
  padding: 10px 14px;
  border-radius: 10px;
  cursor: pointer;
  border: 1px solid var(--border);
  background: #ffffff;
  color: var(--text);
  transition: box-shadow .18s ease, transform .06s ease, background .18s ease, border-color .18s ease;
}
button:hover { background: #fff; box-shadow: 0 6px 18px rgba(8,15,35,.08); border-color: #DEE3EE; }
button:active { transform: translateY(1px); }
button:disabled { opacity: .6; cursor: not-allowed; }

/* 主按钮：深蓝 */
button.primary {
  background: var(--primary);
  border-color: transparent;
  color: #fff;
}
button.primary:hover { background: #234399; }

/* 次按钮：天蓝 */
button.secondary {
  background: var(--primary-2);
  border-color: transparent;
  color: #fff;
}
button.secondary:hover { filter: brightness(1.05); }

/* 危险按钮：红色（用于“停止”等） */
button.danger {
  background: var(--danger);
  border-color: transparent;
  color: #fff;
}
button.danger:hover { filter: brightness(0.95); }

/* 幽灵按钮：轻量选项 */
button.ghost { background: #fff; color: #1f2937; border-color: #DDE2ED; }
button.ghost:hover { background: #F4F7FF; }

/* ================ Log ================ */
.log {
  height: 320px;
  overflow: auto;
  background: #ffffff;
  border-radius: 10px;
  border: 1px solid var(--border);
  padding: 12px;
  white-space: pre-wrap;
  resize: vertical;
  box-shadow: var(--shadow-sm);
}

/* ================ Footer ================ */
footer { margin-top: 12px; color: var(--muted); text-align: center; }

/* ================ Responsive ================ */
@media (max-width: 860px) {
  .grid { grid-template-columns: 1fr; }
}

/* ================ Input with browse button ================ */
.input-with-btn { display: grid; grid-template-columns: 1fr auto; gap: 8px; align-items: center; }
.input-with-btn button.browse { white-space: nowrap; }

/* ================ Modal ================ */
.modal {
  position: fixed; inset: 0;
  background: rgba(8,15,35,.28);           /* 冷色半透明，科研范 */
  backdrop-filter: blur(4px);
  display: flex; align-items: center; justify-content: center; z-index: 1000;
}
.modal.hidden { display: none; }
.modal-content {
  width: min(800px, 92vw); max-height: 80vh; overflow: hidden;
  background: linear-gradient(180deg, var(--panel), var(--panel-2));
  border: 1px solid var(--border);
  border-radius: var(--radius);
  padding: 14px;
  box-shadow: var(--shadow-md);
}
.modal-header { display: grid; grid-template-columns: 1fr auto; gap: 8px; align-items: center; margin-bottom: 10px; }
.modal-header-path {
  color: var(--muted);
  font-family: ui-monospace, SFMono-Regular, Menlo, Consolas, monospace;
  overflow: hidden; text-overflow: ellipsis; white-space: nowrap;
}
.modal-actions { display: flex; gap: 8px; }
.fs-list { border: 1px solid var(--border); border-radius: 8px; overflow: auto; background: #fff; max-height: 60vh; box-shadow: var(--shadow-sm); }
.fs-entry { display: grid; grid-template-columns: 24px 1fr auto; gap: 10px; padding: 10px 12px; border-bottom: 1px solid var(--border); cursor: pointer; align-items: center; transition: background .15s ease; }
.fs-entry:last-child { border-bottom: none; }
.fs-entry:hover { background: #f6f7ff; }
.fs-entry .type { font-weight: 600; color: var(--muted); }
.fs-entry .name { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.fs-entry .meta { color: var(--muted); font-size: 12px; }
.fs-entry.disabled { opacity: .5; cursor: not-allowed; }

/* ================ Control point modal ================ */
.cp-modal { width: min(720px, 92vw); }
.cp-summary { display: flex; flex-direction: column; gap: 12px; }
.cp-meta { font-size: 13px; }
.cp-table-wrapper {
  max-height: 360px;
  overflow: auto;
  border: 1px solid var(--border);
  border-radius: 8px;
  background: #fff;
  box-shadow: var(--shadow-sm);
}
.cp-table { width: 100%; border-collapse: collapse; font-size: 13px; }
.cp-table th,
.cp-table td {
  padding: 8px 10px;
  border-bottom: 1px solid var(--border);
  text-align: right;
  white-space: nowrap;
}
.cp-table th { position: sticky; top: 0; background: #f9faff; z-index: 1; }
.cp-table tbody tr:nth-child(even) { background: rgba(249, 250, 255, 0.5); }
.cp-table tbody tr:hover { background: #f1f5ff; }
.cp-table th:nth-child(1),
.cp-table td:nth-child(1),
.cp-table th:nth-child(2),
.cp-table td:nth-child(2) { text-align: left; }
.cp-table td:nth-child(7) { font-variant-numeric: tabular-nums; }
.cp-empty { text-align: center; padding: 20px 0; color: var(--muted); }
.delta-mid { color: #2563eb; font-weight: 600; }
.delta-high { color: var(--danger); font-weight: 600; }

/* ================ Visible drag handle (optional) ================ */
.log-resizer { height: 10px; cursor: ns-resize; display: flex; align-items: center; justify-content: center; color: var(--muted); }
.log-resizer::after { content: '⋮'; }

/* ================ Soft notice (淡黄高亮，用时可给块加 class="notice-soft") ================ */
.notice-soft {
  background: var(--accent);
  border: 1px solid #F4E7B4;
  color: #705e1c;
  border-radius: 10px;
  padding: 10px 12px;
}

/* ================ Text Utilities ================ */
.text-primary { color: var(--primary) !important; }
.text-muted { color: var(--muted) !important; }
.text-success { color: var(--green) !important; }
.text-accent { color: #705e1c !important; }
.lead { font-size: 16px; font-weight: 600; }

/* ================ Preview panes ================ */
.preview-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 16px; align-items: start; }
.pane { display: flex; flex-direction: column; gap: 8px; }
.pane-title { font-weight: 600; color: var(--primary); }
.pane-canvas {
  width: 100%;
  max-width: 800px;
  aspect-ratio: 1 / 1; /* 固定正方形显示区域，匹配后端生成尺寸 */
  border: 1px solid var(--border);
  border-radius: 10px;
  background: #fff;
  box-shadow: var(--shadow-sm);
  position: relative;
  overflow: hidden;
  touch-action: none; /* 允许自定义缩放/滚轮 */
}
.pane-canvas img {
  width: 100%; height: 100%;
  object-fit: contain;
  image-rendering: pixelated;
  transform-origin: 0 0;
  user-select: none;
  -webkit-user-drag: none;
  will-change: transform;
}

@media (max-width: 860px) {
  .preview-grid { grid-template-columns: 1fr; }
}
